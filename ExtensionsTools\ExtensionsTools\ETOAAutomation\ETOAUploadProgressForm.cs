using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ET;

namespace ET.ETOAAutomation
{
    /// <summary>
    /// 文件上传进度监控窗体
    /// </summary>
    public partial class ETOAUploadProgressForm : Form
    {
        #region 私有字段

        private readonly ETOAFileUploader _fileUploader;
        private readonly System.Windows.Forms.Timer _updateTimer;
        private readonly Dictionary<string, UploadProgressItem> _uploadItems;
        private bool _isClosing = false;

        #endregion 私有字段

        #region 构造函数

        /// <summary>
        /// 初始化上传进度监控窗体
        /// </summary>
        /// <param name="fileUploader">文件上传器</param>
        public ETOAUploadProgressForm(ETOAFileUploader fileUploader)
        {
            _fileUploader = fileUploader ?? throw new ArgumentNullException(nameof(fileUploader));
            _uploadItems = new Dictionary<string, UploadProgressItem>();
            _updateTimer = new System.Windows.Forms.Timer();

            InitializeComponent();
            InitializeTimer();
            SetupEventHandlers();

            ETLogManager.Info("ETOAUploadProgressForm", "上传进度监控窗体初始化完成");
        }

        #endregion 构造函数

        #region 初始化方法

        /// <summary>
        /// 初始化定时器
        /// </summary>
        private void InitializeTimer()
        {
            _updateTimer.Interval = 1000; // 每秒更新一次
            _updateTimer.Tick += UpdateTimer_Tick;
            _updateTimer.Start();
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            this.FormClosing += ETOAUploadProgressForm_FormClosing;
            this.Load += ETOAUploadProgressForm_Load;
        }

        #endregion 初始化方法

        #region 事件处理

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void ETOAUploadProgressForm_Load(object sender, EventArgs e)
        {
            RefreshUploadList();
            UpdateStatistics();
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void ETOAUploadProgressForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            _isClosing = true;
            _updateTimer?.Stop();
            _updateTimer?.Dispose();
        }

        /// <summary>
        /// 定时器更新事件
        /// </summary>
        private void UpdateTimer_Tick(object sender, EventArgs e)
        {
            if (_isClosing) return;

            try
            {
                RefreshUploadList();
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadProgressForm", $"更新进度时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 暂停按钮点击事件
        /// </summary>
        private void BtnPause_Click(object sender, EventArgs e)
        {
            var selectedItem = GetSelectedUploadItem();
            if (selectedItem != null)
            {
                if (selectedItem.IsPaused)
                {
                    _fileUploader.ResumeUpload(selectedItem.FilePath);
                    ETLogManager.Info("ETOAUploadProgressForm", $"恢复上传: {selectedItem.FileName}");
                }
                else
                {
                    _fileUploader.PauseUpload(selectedItem.FilePath);
                    ETLogManager.Info("ETOAUploadProgressForm", $"暂停上传: {selectedItem.FileName}");
                }
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            var selectedItem = GetSelectedUploadItem();
            if (selectedItem != null)
            {
                var result = MessageBox.Show($"确定要取消上传文件 '{selectedItem.FileName}' 吗？",
                    "确认取消", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _fileUploader.CancelUpload(selectedItem.FilePath);
                    ETLogManager.Info("ETOAUploadProgressForm", $"取消上传: {selectedItem.FileName}");
                }
            }
        }

        /// <summary>
        /// 清除已完成按钮点击事件
        /// </summary>
        private void BtnClearCompleted_Click(object sender, EventArgs e)
        {
            var completedItems = _uploadItems.Values.Where(item => item.IsCompleted).ToList();
            foreach (var item in completedItems)
            {
                _fileUploader.ClearUploadSession(item.FilePath);
                _uploadItems.Remove(item.FilePath);
            }

            RefreshUploadList();
            ETLogManager.Info("ETOAUploadProgressForm", $"清除了 {completedItems.Count} 个已完成的上传项");
        }

        /// <summary>
        /// 全部取消按钮点击事件
        /// </summary>
        private void BtnCancelAll_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("确定要取消所有正在进行的上传吗？",
                "确认取消", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                var activeItems = _uploadItems.Values.Where(item => !item.IsCompleted && !item.IsCancelled).ToList();
                foreach (var item in activeItems)
                {
                    _fileUploader.CancelUpload(item.FilePath);
                }

                ETLogManager.Info("ETOAUploadProgressForm", $"取消了 {activeItems.Count} 个活跃的上传项");
            }
        }

        #endregion 事件处理

        #region 私有方法

        /// <summary>
        /// 刷新上传列表
        /// </summary>
        private void RefreshUploadList()
        {
            var sessions = _fileUploader.GetActiveUploadSessions();

            // 更新现有项目
            foreach (var session in sessions)
            {
                if (_uploadItems.ContainsKey(session.FilePath))
                {
                    UpdateUploadItem(_uploadItems[session.FilePath], session);
                }
                else
                {
                    AddUploadItem(session);
                }
            }

            // 移除不存在的项目
            var sessionPaths = sessions.Select(s => s.FilePath).ToHashSet();
            var itemsToRemove = _uploadItems.Keys.Where(path => !sessionPaths.Contains(path)).ToList();
            foreach (var path in itemsToRemove)
            {
                RemoveUploadItem(path);
            }
        }

        /// <summary>
        /// 添加上传项目
        /// </summary>
        private void AddUploadItem(ETOAFileUploader.UploadSession session)
        {
            var item = new UploadProgressItem
            {
                FilePath = session.FilePath,
                FileName = session.FileName,
                FileSize = session.FileSize,
                UploadedBytes = session.UploadedBytes,
                ProgressPercentage = session.ProgressPercentage,
                UploadSpeed = session.UploadSpeed,
                StartTime = session.StartTime,
                IsCompleted = session.IsCompleted,
                IsPaused = session.IsPaused,
                IsCancelled = session.IsCancelled
            };

            _uploadItems[session.FilePath] = item;
            AddItemToListView(item);
        }

        /// <summary>
        /// 更新上传项目
        /// </summary>
        private void UpdateUploadItem(UploadProgressItem item, ETOAFileUploader.UploadSession session)
        {
            item.UploadedBytes = session.UploadedBytes;
            item.ProgressPercentage = session.ProgressPercentage;
            item.UploadSpeed = session.UploadSpeed;
            item.IsCompleted = session.IsCompleted;
            item.IsPaused = session.IsPaused;
            item.IsCancelled = session.IsCancelled;

            UpdateItemInListView(item);
        }

        /// <summary>
        /// 移除上传项目
        /// </summary>
        private void RemoveUploadItem(string filePath)
        {
            if (_uploadItems.ContainsKey(filePath))
            {
                RemoveItemFromListView(_uploadItems[filePath]);
                _uploadItems.Remove(filePath);
            }
        }

        /// <summary>
        /// 添加项目到列表视图
        /// </summary>
        private void AddItemToListView(UploadProgressItem item)
        {
            var listViewItem = new ListViewItem(item.FileName);
            listViewItem.SubItems.Add(FormatFileSize(item.FileSize));
            listViewItem.SubItems.Add($"{item.ProgressPercentage}%");
            listViewItem.SubItems.Add(FormatSpeed(item.UploadSpeed));
            listViewItem.SubItems.Add(GetStatusText(item));
            listViewItem.Tag = item;

            LvUploads.Items.Add(listViewItem);
        }

        /// <summary>
        /// 更新列表视图中的项目
        /// </summary>
        private void UpdateItemInListView(UploadProgressItem item)
        {
            foreach (ListViewItem listViewItem in LvUploads.Items)
            {
                if (listViewItem.Tag == item)
                {
                    listViewItem.SubItems[2].Text = $"{item.ProgressPercentage}%";
                    listViewItem.SubItems[3].Text = FormatSpeed(item.UploadSpeed);
                    listViewItem.SubItems[4].Text = GetStatusText(item);
                    break;
                }
            }
        }

        /// <summary>
        /// 从列表视图移除项目
        /// </summary>
        private void RemoveItemFromListView(UploadProgressItem item)
        {
            foreach (ListViewItem listViewItem in LvUploads.Items)
            {
                if (listViewItem.Tag == item)
                {
                    LvUploads.Items.Remove(listViewItem);
                    break;
                }
            }
        }

        /// <summary>
        /// 获取选中的上传项目
        /// </summary>
        private UploadProgressItem GetSelectedUploadItem()
        {
            if (LvUploads.SelectedItems.Count > 0)
            {
                return LvUploads.SelectedItems[0].Tag as UploadProgressItem;
            }
            return null;
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            var stats = _fileUploader.GetUploadStatistics();

            LblTotalFiles.Text = $"总文件数: {stats.TotalSessions}";
            LblCompletedFiles.Text = $"已完成: {stats.CompletedSessions}";
            LblActiveFiles.Text = $"进行中: {stats.ActiveSessions}";
            LblTotalSize.Text = $"总大小: {FormatFileSize(stats.TotalBytes)}";
            LblUploadedSize.Text = $"已上传: {FormatFileSize(stats.UploadedBytes)}";
            LblOverallProgress.Text = $"总进度: {stats.OverallProgress}%";
            LblAverageSpeed.Text = $"平均速度: {FormatSpeed((long)stats.AverageSpeed)}";

            // 更新总体进度条
            ProgressBarOverall.Value = Math.Min(stats.OverallProgress, 100);
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 格式化上传速度
        /// </summary>
        private string FormatSpeed(long bytesPerSecond)
        {
            if (bytesPerSecond <= 0) return "0 B/s";
            return $"{FormatFileSize(bytesPerSecond)}/s";
        }

        /// <summary>
        /// 获取状态文本
        /// </summary>
        private string GetStatusText(UploadProgressItem item)
        {
            if (item.IsCancelled) return "已取消";
            if (item.IsCompleted) return "已完成";
            if (item.IsPaused) return "已暂停";
            return "上传中";
        }

        #endregion 私有方法

        #region 内部类

        /// <summary>
        /// 上传进度项目
        /// </summary>
        private class UploadProgressItem
        {
            public string FilePath { get; set; }
            public string FileName { get; set; }
            public long FileSize { get; set; }
            public long UploadedBytes { get; set; }
            public int ProgressPercentage { get; set; }
            public long UploadSpeed { get; set; }
            public DateTime StartTime { get; set; }
            public bool IsCompleted { get; set; }
            public bool IsPaused { get; set; }
            public bool IsCancelled { get; set; }
        }

        #endregion 内部类
    }
}