using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using System.Net;
using Flurl.Http;
using Flurl.Http.Configuration;
using Newtonsoft.Json;
using ET.ETOAAutomation.Models;
using ET;

namespace ET.ETOAAutomation
{
    /// <summary>
    /// 基于Flurl.Http的API交互客户端，处理所有HTTP请求
    /// </summary>
    public class ETOAApiClient : IDisposable
    {
        #region 私有字段

        private readonly IFlurlClient _httpClient;
        private ETOALoginInfo _loginInfo;
        private readonly SemaphoreSlim _concurrencySemaphore;
        private readonly ConcurrentDictionary<string, object> _requestCache;
        private readonly object _cacheLock = new object(); // 保留锁对象用于复杂操作
        private static readonly JsonSerializerSettings _jsonSettings;
        private string _currentUserId; // 当前用户ID，用于缓存键生成
        private bool _disposed = false; // 资源释放状态标志

        #endregion 私有字段

        #region 公共属性

        /// <summary>
        /// API基础URL
        /// </summary>
        public string BaseUrl { get; set; }

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// 是否已认证
        /// </summary>
        public bool IsAuthenticated => _loginInfo != null && _loginInfo.IsSuccess;

        /// <summary>
        /// 最大并发请求数
        /// </summary>
        public int MaxConcurrentRequests { get; set; } = 10;

        /// <summary>
        /// 是否启用请求缓存
        /// </summary>
        public bool EnableRequestCache { get; set; } = true;

        /// <summary>
        /// 缓存过期时间（分钟）
        /// </summary>
        public int CacheExpirationMinutes { get; set; } = 5;

        /// <summary>
        /// 默认重试次数
        /// </summary>
        public int DefaultRetryCount { get; set; } = 3;

        /// <summary>
        /// 重试间隔（毫秒）
        /// </summary>
        public int RetryIntervalMs { get; set; } = 1000;

        #endregion 公共属性

        #region 静态构造函数

        /// <summary>
        /// 静态构造函数，初始化JSON序列化设置
        /// </summary>
        static ETOAApiClient()
        {
            _jsonSettings = new JsonSerializerSettings
            {
                DateFormatHandling = DateFormatHandling.IsoDateFormat,
                NullValueHandling = NullValueHandling.Ignore,
                DefaultValueHandling = DefaultValueHandling.Include,
                Formatting = Formatting.None
            };
        }

        #endregion 静态构造函数

        #region 构造函数

        /// <summary>
        /// 初始化API客户端
        /// </summary>
        /// <param name="baseUrl">API基础URL</param>
        public ETOAApiClient(string baseUrl)
        {
            BaseUrl = baseUrl ?? throw new ArgumentNullException(nameof(baseUrl));
            _httpClient = new FlurlClient(BaseUrl);
            _concurrencySemaphore = new SemaphoreSlim(MaxConcurrentRequests, MaxConcurrentRequests);
            _requestCache = new ConcurrentDictionary<string, object>();
            ConfigureHttpClient();
            ETLogManager.Info($"ETOAApiClient初始化完成，BaseUrl: {baseUrl}");
        }

        #endregion 构造函数

        #region 私有方法

        /// <summary>
        /// 配置HTTP客户端
        /// </summary>
        private void ConfigureHttpClient()
        {
            _httpClient.Settings.Timeout = TimeSpan.FromSeconds(TimeoutSeconds);
            _httpClient.Settings.AllowedHttpStatusRange = "200-299,400-499";
        }

        /// <summary>
        /// 应用认证信息到请求
        /// </summary>
        /// <param name="request">HTTP请求</param>
        /// <returns>配置后的请求</returns>
        private IFlurlRequest ApplyAuthentication(IFlurlRequest request)
        {
            if (_loginInfo?.Cookies != null)
            {
                foreach (var cookie in _loginInfo.Cookies)
                {
                    request = request.WithCookie(cookie.Key, cookie.Value);
                }
            }

            if (_loginInfo?.Headers != null)
            {
                foreach (var header in _loginInfo.Headers)
                {
                    request = request.WithHeader(header.Key, header.Value);
                }
            }

            return request;
        }

        /// <summary>
        /// 生成缓存键
        /// </summary>
        /// <param name="method">HTTP方法</param>
        /// <param name="endpoint">API端点</param>
        /// <param name="data">请求数据</param>
        /// <returns>缓存键</returns>
        private string GenerateCacheKey(string method, string endpoint, object data = null)
        {
            var keyBuilder = new StringBuilder();
            keyBuilder.Append($"{method}:{endpoint}");

            // 添加数据哈希
            if (data != null)
            {
                var dataJson = JsonConvert.SerializeObject(data, _jsonSettings);
                // 使用更安全的哈希算法，避免哈希冲突
                using (var sha256 = System.Security.Cryptography.SHA256.Create())
                {
                    var hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(dataJson));
                    var hashString = Convert.ToBase64String(hashBytes).Substring(0, 16);
                    keyBuilder.Append($":{hashString}");
                }
            }

            // 添加用户标识避免跨用户缓存冲突
            if (!string.IsNullOrEmpty(_currentUserId))
            {
                keyBuilder.Append($":user:{_currentUserId}");
            }

            // 添加时间戳的小时部分，避免长期缓存
            keyBuilder.Append($":h:{DateTime.Now.Hour}");

            return keyBuilder.ToString();
        }

        /// <summary>
        /// 从缓存获取数据
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="cacheKey">缓存键</param>
        /// <returns>缓存数据</returns>
        private T GetFromCache<T>(string cacheKey)
        {
            if (!EnableRequestCache) return default(T);

            if (_requestCache.TryGetValue(cacheKey, out var cachedValue))
            {
                var cacheItem = cachedValue as CacheItem;
                if (cacheItem != null && !cacheItem.IsExpired)
                {
                    ETLogManager.Info($"从缓存获取数据: {cacheKey}");
                    return (T)cacheItem.Data;
                }
                else if (cacheItem?.IsExpired == true)
                {
                    // 使用TryRemove确保线程安全
                    _requestCache.TryRemove(cacheKey, out _);
                }
            }
            return default(T);
        }

        /// <summary>
        /// 设置缓存数据
        /// </summary>
        /// <param name="cacheKey">缓存键</param>
        /// <param name="data">数据</param>
        private void SetCache(string cacheKey, object data)
        {
            if (!EnableRequestCache) return;

            var cacheItem = new CacheItem
            {
                Data = data,
                ExpirationTime = DateTime.Now.AddMinutes(CacheExpirationMinutes)
            };

            // 使用AddOrUpdate确保线程安全
            _requestCache.AddOrUpdate(cacheKey, cacheItem, (key, oldValue) => cacheItem);

            ETLogManager.Info($"数据已缓存: {cacheKey}");
        }

        /// <summary>
        /// 执行带重试的HTTP请求
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="requestFunc">请求执行函数</param>
        /// <param name="endpoint">API端点</param>
        /// <param name="retryCount">重试次数</param>
        /// <returns>响应数据</returns>
        private async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> requestFunc, string endpoint, int retryCount = -1)
        {
            if (retryCount == -1) retryCount = DefaultRetryCount;

            Exception lastException = null;

            for (int attempt = 0; attempt <= retryCount; attempt++)
            {
                try
                {
                    await _concurrencySemaphore.WaitAsync();
                    try
                    {
                        return await requestFunc();
                    }
                    finally
                    {
                        _concurrencySemaphore.Release();
                    }
                }
                catch (FlurlHttpTimeoutException ex)
                {
                    lastException = ex;
                    ETLogManager.Warn($"请求超时 (尝试 {attempt + 1}/{retryCount + 1}): {endpoint}");

                    if (attempt < retryCount)
                    {
                        await Task.Delay(RetryIntervalMs * (attempt + 1)); // 指数退避
                    }
                }
                catch (FlurlHttpException ex) when (IsRetryableError(ex))
                {
                    lastException = ex;
                    ETLogManager.Warn($"可重试错误 (尝试 {attempt + 1}/{retryCount + 1}): {endpoint}, 状态码: {ex.StatusCode}");

                    if (attempt < retryCount)
                    {
                        await Task.Delay(RetryIntervalMs * (attempt + 1)); // 指数退避
                    }
                }
                catch (Exception ex)
                {
                    // 不可重试的错误，直接抛出
                    ETLogManager.Error($"不可重试错误: {endpoint}", ex);
                    throw;
                }
            }

            // 所有重试都失败了
            ETLogManager.Error($"请求最终失败: {endpoint}, 重试次数: {retryCount}", lastException);
            throw new ETException($"请求失败，已重试{retryCount}次: {lastException?.Message}", "ExecuteWithRetryAsync", lastException);
        }

        /// <summary>
        /// 判断是否为可重试的错误
        /// </summary>
        /// <param name="ex">HTTP异常</param>
        /// <returns>是否可重试</returns>
        private bool IsRetryableError(FlurlHttpException ex)
        {
            // 5xx服务器错误通常可以重试
            if (ex.StatusCode >= 500) return true;

            // 429 Too Many Requests 可以重试
            if (ex.StatusCode == 429) return true;

            // 408 Request Timeout 可以重试
            if (ex.StatusCode == 408) return true;

            // 其他4xx客户端错误通常不应重试
            return false;
        }

        /// <summary>
        /// 处理API响应并转换为强类型
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="response">HTTP响应</param>
        /// <returns>强类型数据</returns>
        private async Task<T> ProcessResponseAsync<T>(IFlurlResponse response)
        {
            try
            {
                var content = await response.GetStringAsync();

                if (typeof(T) == typeof(string))
                {
                    return (T)(object)content;
                }

                if (string.IsNullOrEmpty(content))
                {
                    return default(T);
                }

                return JsonConvert.DeserializeObject<T>(content, _jsonSettings);
            }
            catch (JsonException ex)
            {
                ETLogManager.Error($"JSON反序列化失败: {ex.Message}");
                throw new ETException($"响应数据格式错误: {ex.Message}", "ProcessResponseAsync", ex);
            }
        }

        #endregion 私有方法

        #region 公共方法

        /// <summary>
        /// 设置认证信息
        /// </summary>
        /// <param name="loginInfo">登录信息</param>
        public void SetAuthenticationInfo(ETOALoginInfo loginInfo)
        {
            _loginInfo = loginInfo ?? throw new ArgumentNullException(nameof(loginInfo));

            // 设置当前用户ID用于缓存键生成
            _currentUserId = loginInfo.UserId ?? loginInfo.Username ?? Guid.NewGuid().ToString("N")[..8];

            // 清除旧的缓存，因为用户已更改
            ClearCache();

            ETLogManager.Info($"API客户端认证信息设置完成，用户ID: {_currentUserId}");
        }

        /// <summary>
        /// GET请求
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <returns>响应数据</returns>
        public async Task<T> GetAsync<T>(string endpoint)
        {
            return await GetAsync<T>(endpoint, null, DefaultRetryCount);
        }

        /// <summary>
        /// GET请求（带查询参数）
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <param name="queryParams">查询参数</param>
        /// <param name="retryCount">重试次数</param>
        /// <returns>响应数据</returns>
        public async Task<T> GetAsync<T>(string endpoint, object queryParams = null, int retryCount = -1)
        {
            ThrowIfDisposed();
            var cacheKey = GenerateCacheKey("GET", endpoint, queryParams);

            // 尝试从缓存获取
            var cachedResult = GetFromCache<T>(cacheKey);
            if (cachedResult != null)
            {
                return cachedResult;
            }

            return await ExecuteWithRetryAsync(async () =>
            {
                var request = _httpClient.Request(endpoint);
                request = ApplyAuthentication(request);

                // 添加查询参数
                if (queryParams != null)
                {
                    request = request.SetQueryParams(queryParams);
                }

                ETLogManager.Info($"发送GET请求: {endpoint}");
                var response = await request.GetAsync();
                var result = await ProcessResponseAsync<T>(response);

                // 缓存结果
                SetCache(cacheKey, result);

                return result;
            }, endpoint, retryCount);
        }

        /// <summary>
        /// POST请求
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <param name="data">请求数据</param>
        /// <returns>响应数据</returns>
        public async Task<T> PostAsync<T>(string endpoint, object data)
        {
            ThrowIfDisposed();
            return await PostAsync<T>(endpoint, data, DefaultRetryCount);
        }

        /// <summary>
        /// POST请求（带重试）
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <param name="data">请求数据</param>
        /// <param name="retryCount">重试次数</param>
        /// <returns>响应数据</returns>
        public async Task<T> PostAsync<T>(string endpoint, object data, int retryCount)
        {
            ThrowIfDisposed();
            return await ExecuteWithRetryAsync(async () =>
            {
                var request = _httpClient.Request(endpoint);
                request = ApplyAuthentication(request);

                ETLogManager.Info($"发送POST请求: {endpoint}");
                var response = await request.PostJsonAsync(data);
                return await ProcessResponseAsync<T>(response);
            }, endpoint, retryCount);
        }

        /// <summary>
        /// POST表单数据请求
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <param name="formData">表单数据</param>
        /// <param name="retryCount">重试次数</param>
        /// <returns>响应数据</returns>
        public async Task<T> PostFormAsync<T>(string endpoint, object formData, int retryCount = -1)
        {
            if (retryCount == -1) retryCount = DefaultRetryCount;

            return await ExecuteWithRetryAsync(async () =>
            {
                var request = _httpClient.Request(endpoint);
                request = ApplyAuthentication(request);

                ETLogManager.Info($"发送POST表单请求: {endpoint}");
                var response = await request.PostUrlEncodedAsync(formData);
                return await ProcessResponseAsync<T>(response);
            }, endpoint, retryCount);
        }

        /// <summary>
        /// PUT请求
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <param name="data">请求数据</param>
        /// <returns>响应数据</returns>
        public async Task<T> PutAsync<T>(string endpoint, object data)
        {
            return await PutAsync<T>(endpoint, data, DefaultRetryCount);
        }

        /// <summary>
        /// PUT请求（带重试）
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <param name="data">请求数据</param>
        /// <param name="retryCount">重试次数</param>
        /// <returns>响应数据</returns>
        public async Task<T> PutAsync<T>(string endpoint, object data, int retryCount)
        {
            return await ExecuteWithRetryAsync(async () =>
            {
                var request = _httpClient.Request(endpoint);
                request = ApplyAuthentication(request);

                ETLogManager.Info($"发送PUT请求: {endpoint}");
                var response = await request.PutJsonAsync(data);
                return await ProcessResponseAsync<T>(response);
            }, endpoint, retryCount);
        }

        /// <summary>
        /// DELETE请求
        /// </summary>
        /// <param name="endpoint">API端点</param>
        /// <returns>删除是否成功</returns>
        public async Task<bool> DeleteAsync(string endpoint)
        {
            return await DeleteAsync(endpoint, DefaultRetryCount);
        }

        /// <summary>
        /// DELETE请求（带重试）
        /// </summary>
        /// <param name="endpoint">API端点</param>
        /// <param name="retryCount">重试次数</param>
        /// <returns>删除是否成功</returns>
        public async Task<bool> DeleteAsync(string endpoint, int retryCount)
        {
            return await ExecuteWithRetryAsync(async () =>
            {
                var request = _httpClient.Request(endpoint);
                request = ApplyAuthentication(request);

                ETLogManager.Info($"发送DELETE请求: {endpoint}");
                var response = await request.DeleteAsync();
                return response.ResponseMessage.IsSuccessStatusCode;
            }, endpoint, retryCount);
        }

        /// <summary>
        /// 批量GET请求
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoints">API端点列表</param>
        /// <returns>响应数据列表</returns>
        public async Task<List<T>> BatchGetAsync<T>(IEnumerable<string> endpoints)
        {
            var tasks = endpoints.Select(endpoint => GetAsync<T>(endpoint));
            var results = await Task.WhenAll(tasks);
            return results.ToList();
        }

        /// <summary>
        /// 清除请求缓存
        /// </summary>
        public void ClearCache()
        {
            _requestCache.Clear();
            ETLogManager.Info("请求缓存已清除");
        }

        /// <summary>
        /// 清除过期缓存
        /// </summary>
        public void ClearExpiredCache()
        {
            var expiredKeys = _requestCache
                .Where(kv => kv.Value is CacheItem item && item.IsExpired)
                .Select(kv => kv.Key)
                .ToList();

            int removedCount = 0;
            foreach (var key in expiredKeys)
            {
                if (_requestCache.TryRemove(key, out _))
                {
                    removedCount++;
                }
            }

            ETLogManager.Info($"已清除{removedCount}个过期缓存项");
        }

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        /// <returns>缓存统计</returns>
        public (int TotalItems, int ExpiredItems) GetCacheStats()
        {
            var total = _requestCache.Count;
            var expired = _requestCache.Count(kv => kv.Value is CacheItem item && item.IsExpired);
            return (total, expired);
        }

        /// <summary>
        /// 测试连接
        /// </summary>
        /// <param name="endpoint">测试端点</param>
        /// <returns>连接是否成功</returns>
        public async Task<bool> TestConnectionAsync(string endpoint = "")
        {
            try
            {
                var testEndpoint = string.IsNullOrEmpty(endpoint) ? "/" : endpoint;
                var request = _httpClient.Request(testEndpoint);
                request = ApplyAuthentication(request);

                var response = await request.GetAsync();
                return response.ResponseMessage.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"连接测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    // 释放HTTP客户端
                    if (_httpClient != null)
                    {
                        _httpClient.Dispose();
                        ETLogManager.Info("HTTP客户端已释放");
                    }

                    // 释放并发信号量
                    if (_concurrencySemaphore != null)
                    {
                        _concurrencySemaphore.Dispose();
                        ETLogManager.Info("并发信号量已释放");
                    }

                    // 清理缓存
                    ClearCache();
                    ETLogManager.Info("缓存已清理");

                    _disposed = true;
                    ETLogManager.Info("ETOAApiClient资源释放完成");
                }
                catch (Exception ex)
                {
                    ETLogManager.Error("释放ETOAApiClient资源时发生异常", ex);
                }
            }
        }

        /// <summary>
        /// 检查对象是否已释放
        /// </summary>
        private void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(ETOAApiClient));
            }
        }

        #endregion 公共方法

        #region 内部类

        /// <summary>
        /// 缓存项
        /// </summary>
        private class CacheItem
        {
            public object Data { get; set; }
            public DateTime ExpirationTime { get; set; }
            public bool IsExpired => DateTime.Now > ExpirationTime;
        }

        #endregion 内部类
    }
}