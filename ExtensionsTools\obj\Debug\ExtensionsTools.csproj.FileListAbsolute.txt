\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\runtimes\win-x86\native\WebView2Loader.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\runtimes\win-x64\native\WebView2Loader.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\runtimes\win-arm64\native\WebView2Loader.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\ExtensionsTools.dll.config
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\ExtensionsTools.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\ExtensionsTools.pdb
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Aliyun.OSS.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\DotNetZip.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Extensions.DependencyInjection.Abstractions.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Extensions.Logging.Abstractions.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Office.Tools.Common.v4.0.Utilities.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Office.Tools.Excel.v4.0.Utilities.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Office.Tools.Word.v4.0.Utilities.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Web.WebView2.Core.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Web.WebView2.WinForms.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Web.WebView2.Wpf.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\NetTopologySuite.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Newtonsoft.Json.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\OpenAI.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Buffers.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.ClientModel.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Diagnostics.DiagnosticSource.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.IO.Pipelines.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Memory.Data.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Memory.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Numerics.Vectors.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Text.Encodings.Web.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Text.Json.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Threading.Tasks.Extensions.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.ValueTuple.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Aliyun.OSS.pdb
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Aliyun.OSS.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\DotNetZip.pdb
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\DotNetZip.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Extensions.DependencyInjection.Abstractions.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Extensions.Logging.Abstractions.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Web.WebView2.Core.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Web.WebView2.WinForms.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Web.WebView2.Wpf.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\NetTopologySuite.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Newtonsoft.Json.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\OpenAI.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Buffers.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.ClientModel.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Diagnostics.DiagnosticSource.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.IO.Pipelines.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Memory.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Memory.Data.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Numerics.Vectors.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Text.Encodings.Web.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Text.Json.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Threading.Tasks.Extensions.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.ValueTuple.xml
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ExtensionsTools.csproj.AssemblyReference.cache
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ExtensionsTools.csproj.ResolveComReference.cache
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.HHUcDirectorySelect.resources
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.ETLicense.ETAboutLicenseForm.resources
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.ETLicense.ETLicenseAdminLoginForm.resources
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.ETLicense.ETLicenseGeneratorForm.resources
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.ETUcFileSelect.resources
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ExtensionsTools.csproj.GenerateResource.cache
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ExtensionsTools.csproj.CoreCompileInputs.cache
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\Extensio.3DBDD257.Up2Date
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ExtensionsTools.dll
\\DESKTOP-QUE4EMJ\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ExtensionsTools.pdb
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ExtensionsTools.csproj.AssemblyReference.cache
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.Controls.HHUcDirectorySelect.resources
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.ETLicense.ETAboutLicenseForm.resources
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.ETLicense.ETLicenseAdminLoginForm.resources
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.ETLicense.ETLicenseGeneratorForm.resources
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.ETLicense.ETLocationTestForm.resources
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.ETLoginWebBrowser.ETLoginWebBrowser.resources
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.Controls.ETUcFileSelect.resources
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.ETOAAutomation.ETOALoginBrowser.resources
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.ETOAAutomation.ETOASimulationBrowser.resources
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.ETOAAutomation.ETOAUploadConfigForm.resources
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ET.ETOAAutomation.ETOAUploadProgressForm.resources
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ExtensionsTools.csproj.GenerateResource.cache
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ExtensionsTools.csproj.CoreCompileInputs.cache
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\runtimes\win-x86\native\WebView2Loader.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\runtimes\win-x64\native\WebView2Loader.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\runtimes\win-arm64\native\WebView2Loader.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\chrome_100_percent.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\chrome_200_percent.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\chrome_elf.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\d3dcompiler_47.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\icudtl.dat
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\libcef.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\libEGL.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\libGLESv2.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\resources.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\v8_context_snapshot.bin
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\vk_swiftshader.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\vk_swiftshader_icd.json
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\vulkan-1.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\af.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\am.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\ar.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\bg.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\bn.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\ca.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\cs.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\da.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\de.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\el.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\en-GB.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\en-US.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\es-419.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\es.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\et.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\fa.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\fi.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\fil.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\fr.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\gu.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\he.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\hi.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\hr.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\hu.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\id.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\it.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\ja.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\kn.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\ko.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\lt.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\lv.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\ml.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\mr.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\ms.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\nb.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\nl.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\pl.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\pt-BR.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\pt-PT.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\ro.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\ru.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\sk.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\sl.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\sr.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\sv.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\sw.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\ta.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\te.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\th.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\tr.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\uk.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\ur.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\vi.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\zh-CN.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\locales\zh-TW.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\chrome_100_percent.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\chrome_200_percent.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\chrome_elf.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\d3dcompiler_47.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\dxcompiler.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\dxil.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\icudtl.dat
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\libcef.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\libEGL.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\libGLESv2.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\resources.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\v8_context_snapshot.bin
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\vk_swiftshader.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\vk_swiftshader_icd.json
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\vulkan-1.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\af.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\am.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\ar.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\bg.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\bn.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\ca.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\cs.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\da.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\de.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\el.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\en-GB.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\en-US.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\es-419.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\es.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\et.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\fa.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\fi.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\fil.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\fr.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\gu.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\he.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\hi.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\hr.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\hu.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\id.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\it.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\ja.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\kn.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\ko.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\lt.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\lv.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\ml.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\mr.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\ms.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\nb.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\nl.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\pl.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\pt-BR.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\pt-PT.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\ro.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\ru.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\sk.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\sl.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\sr.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\sv.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\sw.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\ta.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\te.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\th.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\tr.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\uk.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\ur.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\vi.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\zh-CN.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\locales\zh-TW.pak
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\CefSharp.BrowserSubprocess.Core.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\CefSharp.BrowserSubprocess.Core.pdb
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\CefSharp.BrowserSubprocess.exe
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\CefSharp.BrowserSubprocess.pdb
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\CefSharp.Core.Runtime.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\CefSharp.Core.Runtime.pdb
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\CefSharp.Core.Runtime.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\CefSharp.BrowserSubprocess.Core.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\CefSharp.BrowserSubprocess.Core.pdb
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\CefSharp.BrowserSubprocess.exe
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\CefSharp.BrowserSubprocess.pdb
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\CefSharp.Core.Runtime.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\CefSharp.Core.Runtime.pdb
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\CefSharp.Core.Runtime.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x86\CefSharp.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\x64\CefSharp.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\ExtensionsTools.dll.config
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\ExtensionsTools.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\ExtensionsTools.pdb
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Aliyun.OSS.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\CefSharp.Core.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\CefSharp.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\CefSharp.WinForms.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Flurl.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Flurl.Http.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.CSharp.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Extensions.Logging.Abstractions.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Office.Tools.Common.v4.0.Utilities.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Office.Tools.Excel.v4.0.Utilities.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Office.Tools.Word.v4.0.Utilities.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Web.WebView2.Core.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Web.WebView2.WinForms.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Web.WebView2.Wpf.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\NetTopologySuite.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Newtonsoft.Json.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\OpenAI.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\SharpCompress.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Buffers.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.ClientModel.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Collections.Immutable.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Diagnostics.DiagnosticSource.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.IO.Pipelines.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Memory.Data.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Memory.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Numerics.Vectors.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Text.Encodings.Web.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Text.Json.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Threading.Tasks.Extensions.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\ZstdSharp.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.ValueTuple.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Aliyun.OSS.pdb
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Aliyun.OSS.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\CefSharp.pdb
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\CefSharp.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\CefSharp.Core.pdb
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\CefSharp.Core.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\CefSharp.WinForms.pdb
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\CefSharp.WinForms.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Flurl.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Flurl.Http.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Extensions.DependencyInjection.Abstractions.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Extensions.Logging.Abstractions.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Web.WebView2.Core.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Web.WebView2.WinForms.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Microsoft.Web.WebView2.Wpf.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\NetTopologySuite.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\Newtonsoft.Json.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\OpenAI.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\SharpCompress.pdb
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Buffers.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.ClientModel.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Collections.Immutable.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Diagnostics.DiagnosticSource.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.IO.Pipelines.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Memory.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Memory.Data.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Numerics.Vectors.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Text.Encodings.Web.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Text.Json.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\bin\Debug\System.Threading.Tasks.Extensions.xml
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\Extensio.3DBDD257.Up2Date
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ExtensionsTools.dll
D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\obj\Debug\ExtensionsTools.pdb
