# 📤 ETOAFileUploader 文件上传流程详解

## 📋 概述

ETOAFileUploader是专门处理文件上传的模块，支持单文件上传、批量上传、分块上传、断点续传、进度监控等功能，确保大文件和多文件的可靠上传。

## 🏗️ 类结构图

```mermaid
classDiagram
    class ETOAFileUploader {
        +int MaxFileSize
        +List~string~ AllowedExtensions
        +int ChunkSize
        +bool EnableResumableUpload
        +int MaxConcurrentUploads
        +SemaphoreSlim ConcurrencyLimiter
        
        +UploadFileAsync(endpoint, filePath, formData) Task~ETOAUploadResult~
        +UploadFilesAsync(endpoint, filePaths, formData) Task~List~ETOAUploadResult~~
        +UploadWithProgressAsync(endpoint, filePath, progressCallback) Task~ETOAUploadResult~
        +UploadFileResumableAsync(endpoint, filePath, formData) Task~ETOAUploadResult~
        +ValidateFile(filePath) bool
        +SetProgressCallback(callback) void
        
        -UploadFileChunked(endpoint, filePath, formData) Task~ETOAUploadResult~
        -UploadFileDirect(endpoint, filePath, formData) Task~ETOAUploadResult~
        -CreateMultipartContent(filePath, formData) MultipartFormDataContent
        -CalculateFileHash(filePath) string
        -ResumeUpload(endpoint, filePath, uploadId) Task~ETOAUploadResult~
    }
    
    ETOAFileUploader --> ETOAUploadResult
    ETOAFileUploader --> ETOAApiClient
    ETOAFileUploader --> ETOAConfigHelper
    ETOAFileUploader --> ETOAPerformanceHelper
```

## 📁 文件上传决策流程

```mermaid
flowchart TD
    A[开始文件上传] --> B[验证文件有效性]
    B --> C{文件是否有效}
    C -->|无效| D[返回验证错误]
    C -->|有效| E[检查文件大小]
    E --> F{文件大小判断}
    F -->|小文件| G[直接上传模式]
    F -->|大文件| H[分块上传模式]
    F -->|超大文件| I[断点续传模式]
    
    G --> J[创建表单数据]
    J --> K[发送单次请求]
    K --> L[监控上传进度]
    
    H --> M[计算分块信息]
    M --> N[逐块上传]
    N --> O[合并文件块]
    
    I --> P[检查已上传部分]
    P --> Q[从断点继续上传]
    Q --> R[验证文件完整性]
    
    L --> S{上传是否成功}
    O --> S
    R --> S
    S -->|成功| T[返回成功结果]
    S -->|失败| U[处理上传失败]
    U --> V[记录错误日志]
    V --> W[返回失败结果]
    
    style A fill:#e1f5fe
    style T fill:#c8e6c9
    style D fill:#ffcdd2
    style W fill:#ffcdd2
```

## 🔍 文件验证流程

```mermaid
sequenceDiagram
    participant Uploader as ETOAFileUploader
    participant Validator as FileValidator
    participant Config as ETOAConfigHelper
    participant Log as ETLogManager
    
    Uploader->>Validator: ValidateFile(filePath)
    Validator->>Validator: 检查文件是否存在
    
    alt 文件不存在
        Validator-->>Uploader: 返回文件不存在错误
    else 文件存在
        Validator->>Validator: 获取文件信息
        Validator->>Config: 获取最大文件大小限制
        Config-->>Validator: 返回大小限制
        
        Validator->>Validator: 检查文件大小
        alt 文件过大
            Validator->>Log: 记录文件过大日志
            Validator-->>Uploader: 返回文件过大错误
        else 文件大小合适
            Validator->>Config: 获取允许的文件扩展名
            Config-->>Validator: 返回扩展名列表
            
            Validator->>Validator: 检查文件扩展名
            alt 扩展名不允许
                Validator->>Log: 记录扩展名不允许日志
                Validator-->>Uploader: 返回扩展名错误
            else 扩展名允许
                Validator->>Validator: 检查文件完整性
                alt 文件损坏
                    Validator->>Log: 记录文件损坏日志
                    Validator-->>Uploader: 返回文件损坏错误
                else 文件完整
                    Validator->>Log: 记录验证通过日志
                    Validator-->>Uploader: 返回验证成功
                end
            end
        end
    end
```

### 文件验证实现

```csharp
public bool ValidateFile(string filePath)
{
    try
    {
        // 1. 检查文件是否存在
        if (!File.Exists(filePath))
        {
            ETLogManager.Error($"文件不存在: {filePath}");
            return false;
        }
        
        var fileInfo = new FileInfo(filePath);
        
        // 2. 检查文件大小
        var maxSizeMB = ETOAConfigHelper.GetMaxFileSize();
        var maxSizeBytes = maxSizeMB * 1024 * 1024;
        
        if (fileInfo.Length > maxSizeBytes)
        {
            ETLogManager.Error($"文件过大: {filePath}, 大小: {fileInfo.Length / 1024 / 1024}MB, 限制: {maxSizeMB}MB");
            return false;
        }
        
        // 3. 检查文件扩展名
        var extension = fileInfo.Extension.ToLowerInvariant();
        var allowedExtensions = ETOAConfigHelper.GetAllowedFileExtensions();
        
        if (allowedExtensions.Count > 0 && !allowedExtensions.Contains(extension))
        {
            ETLogManager.Error($"文件扩展名不允许: {filePath}, 扩展名: {extension}");
            return false;
        }
        
        // 4. 检查文件是否被占用
        try
        {
            using (var stream = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.Read))
            {
                // 文件可以正常打开
            }
        }
        catch (IOException)
        {
            ETLogManager.Error($"文件被占用或无法访问: {filePath}");
            return false;
        }
        
        // 5. 检查文件内容（可选）
        if (ETOAConfigHelper.GetEnableFileContentValidation())
        {
            if (!ValidateFileContent(filePath, extension))
            {
                ETLogManager.Error($"文件内容验证失败: {filePath}");
                return false;
            }
        }
        
        ETLogManager.Debug($"文件验证通过: {filePath}");
        return true;
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"文件验证异常: {filePath}, 错误: {ex.Message}", ex);
        return false;
    }
}
```

## 📤 单文件上传流程

```mermaid
sequenceDiagram
    participant Client as ETOAClient
    participant Uploader as ETOAFileUploader
    participant Api as ETOAApiClient
    participant Progress as ProgressCallback
    participant Server as OA服务器
    
    Client->>Uploader: UploadFileAsync(endpoint, filePath, formData)
    Uploader->>Uploader: ValidateFile(filePath)
    
    alt 文件验证失败
        Uploader-->>Client: 返回验证错误
    else 文件验证成功
        Uploader->>Uploader: 检查上传模式
        
        alt 直接上传模式
            Uploader->>Uploader: CreateMultipartContent(filePath, formData)
            Uploader->>Api: PostAsync(endpoint, multipartContent)
            
            loop 上传进度监控
                Api->>Progress: 报告上传进度
                Progress-->>Uploader: 进度回调处理
            end
            
            Api->>Server: 发送文件数据
            Server-->>Api: 返回上传结果
            Api-->>Uploader: 返回响应
            
        else 分块上传模式
            Uploader->>Uploader: CalculateChunks(filePath)
            
            loop 逐块上传
                Uploader->>Uploader: ReadFileChunk(chunkIndex)
                Uploader->>Api: PostAsync(chunkEndpoint, chunkData)
                Api->>Server: 发送分块数据
                Server-->>Api: 返回分块结果
                Api-->>Uploader: 分块上传完成
                
                Uploader->>Progress: 更新总体进度
            end
            
            Uploader->>Api: PostAsync(mergeEndpoint, mergeData)
            Api->>Server: 请求合并文件
            Server-->>Api: 返回合并结果
            Api-->>Uploader: 文件合并完成
        end
        
        Uploader->>Uploader: 创建ETOAUploadResult
        Uploader-->>Client: 返回上传结果
    end
```

### 单文件上传实现

```csharp
public async Task<ETOAUploadResult> UploadFileAsync(string endpoint, string filePath, 
    Dictionary<string, object> formData = null)
{
    var uploadId = Guid.NewGuid().ToString();
    var startTime = DateTime.Now;
    
    try
    {
        ETLogManager.Info($"[{uploadId}] 开始上传文件: {filePath}");
        
        // 1. 文件验证
        if (!ValidateFile(filePath))
        {
            return new ETOAUploadResult
            {
                IsSuccess = false,
                ErrorMessage = "文件验证失败",
                FileName = Path.GetFileName(filePath),
                FilePath = filePath
            };
        }
        
        var fileInfo = new FileInfo(filePath);
        var fileSize = fileInfo.Length;
        
        // 2. 选择上传模式
        ETOAUploadResult result;
        if (fileSize > ChunkSize && EnableChunkedUpload)
        {
            // 分块上传
            result = await UploadFileChunked(endpoint, filePath, formData, uploadId);
        }
        else
        {
            // 直接上传
            result = await UploadFileDirect(endpoint, filePath, formData, uploadId);
        }
        
        // 3. 设置上传结果信息
        result.FileName = fileInfo.Name;
        result.FilePath = filePath;
        result.FileSize = fileSize;
        result.StartTime = startTime;
        result.EndTime = DateTime.Now;
        result.Duration = result.EndTime - result.StartTime;
        
        if (result.IsSuccess)
        {
            ETLogManager.Info($"[{uploadId}] 文件上传成功: {filePath}, 耗时: {result.Duration.TotalSeconds:F2}秒");
        }
        else
        {
            ETLogManager.Error($"[{uploadId}] 文件上传失败: {filePath}, 错误: {result.ErrorMessage}");
        }
        
        return result;
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"[{uploadId}] 文件上传异常: {filePath}, 错误: {ex.Message}", ex);
        
        return new ETOAUploadResult
        {
            IsSuccess = false,
            ErrorMessage = $"上传异常: {ex.Message}",
            Exception = ex,
            FileName = Path.GetFileName(filePath),
            FilePath = filePath,
            StartTime = startTime,
            EndTime = DateTime.Now
        };
    }
}
```

## 🧩 分块上传流程

```mermaid
flowchart TD
    A[开始分块上传] --> B[计算文件分块信息]
    B --> C[创建上传会话]
    C --> D[初始化分块索引]
    D --> E[开始分块循环]
    E --> F[读取当前分块数据]
    F --> G[计算分块哈希值]
    G --> H[发送分块到服务器]
    H --> I{分块上传是否成功}
    I -->|失败| J[重试当前分块]
    I -->|成功| K[更新上传进度]
    K --> L[记录分块完成]
    L --> M{是否还有未上传分块}
    M -->|是| N[移动到下一分块]
    M -->|否| O[所有分块上传完成]
    N --> E
    O --> P[发送合并请求]
    P --> Q[等待服务器合并文件]
    Q --> R{合并是否成功}
    R -->|成功| S[验证文件完整性]
    R -->|失败| T[返回合并失败]
    S --> U{文件完整性验证}
    U -->|通过| V[返回上传成功]
    U -->|失败| W[返回完整性验证失败]
    J --> X{重试次数是否超限}
    X -->|否| F
    X -->|是| Y[返回分块上传失败]
    
    style A fill:#e1f5fe
    style V fill:#c8e6c9
    style T fill:#ffcdd2
    style W fill:#ffcdd2
    style Y fill:#ffcdd2
```

### 分块上传实现

```csharp
private async Task<ETOAUploadResult> UploadFileChunked(string endpoint, string filePath, 
    Dictionary<string, object> formData, string uploadId)
{
    try
    {
        var fileInfo = new FileInfo(filePath);
        var totalSize = fileInfo.Length;
        var chunkCount = (int)Math.Ceiling((double)totalSize / ChunkSize);
        
        ETLogManager.Info($"[{uploadId}] 开始分块上传: 文件大小 {totalSize} 字节, 分块数量 {chunkCount}, 分块大小 {ChunkSize} 字节");
        
        // 1. 创建上传会话
        var sessionData = new
        {
            uploadId = uploadId,
            fileName = fileInfo.Name,
            fileSize = totalSize,
            chunkSize = ChunkSize,
            chunkCount = chunkCount,
            fileHash = await CalculateFileHashAsync(filePath)
        };
        
        var sessionResponse = await ApiClient.PostAsync<dynamic>($"{endpoint}/session", sessionData);
        if (!sessionResponse.IsSuccess)
        {
            return new ETOAUploadResult
            {
                IsSuccess = false,
                ErrorMessage = $"创建上传会话失败: {sessionResponse.ErrorMessage}"
            };
        }
        
        // 2. 逐块上传
        long uploadedSize = 0;
        var completedChunks = new List<int>();
        
        using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
        {
            for (int chunkIndex = 0; chunkIndex < chunkCount; chunkIndex++)
            {
                var chunkStartPos = chunkIndex * ChunkSize;
                var chunkActualSize = Math.Min(ChunkSize, totalSize - chunkStartPos);
                
                // 读取分块数据
                var chunkData = new byte[chunkActualSize];
                fileStream.Seek(chunkStartPos, SeekOrigin.Begin);
                await fileStream.ReadAsync(chunkData, 0, (int)chunkActualSize);
                
                // 计算分块哈希
                var chunkHash = CalculateChunkHash(chunkData);
                
                // 上传分块
                var chunkResult = await UploadChunk(endpoint, uploadId, chunkIndex, chunkData, chunkHash);
                
                if (chunkResult.IsSuccess)
                {
                    uploadedSize += chunkActualSize;
                    completedChunks.Add(chunkIndex);
                    
                    // 触发进度回调
                    var progress = (double)uploadedSize / totalSize * 100;
                    OnProgressChanged?.Invoke(new UploadProgressEventArgs
                    {
                        UploadId = uploadId,
                        FileName = fileInfo.Name,
                        TotalSize = totalSize,
                        UploadedSize = uploadedSize,
                        Progress = progress,
                        ChunkIndex = chunkIndex,
                        ChunkCount = chunkCount
                    });
                    
                    ETLogManager.Debug($"[{uploadId}] 分块 {chunkIndex + 1}/{chunkCount} 上传完成, 进度: {progress:F1}%");
                }
                else
                {
                    // 分块上传失败，尝试重试
                    var retryResult = await RetryChunkUpload(endpoint, uploadId, chunkIndex, chunkData, chunkHash);
                    if (!retryResult.IsSuccess)
                    {
                        return new ETOAUploadResult
                        {
                            IsSuccess = false,
                            ErrorMessage = $"分块 {chunkIndex} 上传失败: {retryResult.ErrorMessage}",
                            UploadedSize = uploadedSize,
                            Progress = (double)uploadedSize / totalSize * 100
                        };
                    }
                }
            }
        }
        
        // 3. 请求合并文件
        var mergeData = new
        {
            uploadId = uploadId,
            completedChunks = completedChunks,
            totalChunks = chunkCount
        };
        
        var mergeResponse = await ApiClient.PostAsync<dynamic>($"{endpoint}/merge", mergeData);
        if (!mergeResponse.IsSuccess)
        {
            return new ETOAUploadResult
            {
                IsSuccess = false,
                ErrorMessage = $"文件合并失败: {mergeResponse.ErrorMessage}",
                UploadedSize = uploadedSize,
                Progress = 100
            };
        }
        
        // 4. 验证文件完整性
        var verifyResponse = await ApiClient.GetAsync<dynamic>($"{endpoint}/verify/{uploadId}");
        if (!verifyResponse.IsSuccess || verifyResponse.Data?.isValid != true)
        {
            return new ETOAUploadResult
            {
                IsSuccess = false,
                ErrorMessage = "文件完整性验证失败",
                UploadedSize = uploadedSize,
                Progress = 100
            };
        }
        
        return new ETOAUploadResult
        {
            IsSuccess = true,
            UploadedSize = totalSize,
            Progress = 100,
            ServerResponse = mergeResponse.RawContent,
            FileId = mergeResponse.Data?.fileId?.ToString(),
            FileUrl = mergeResponse.Data?.fileUrl?.ToString(),
            ChunkCount = chunkCount,
            CompletedChunks = completedChunks.Count
        };
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"[{uploadId}] 分块上传异常: {ex.Message}", ex);
        throw;
    }
}
```

## 📊 批量上传流程

```mermaid
sequenceDiagram
    participant Client as ETOAClient
    participant Uploader as ETOAFileUploader
    participant Semaphore as ConcurrencyLimiter
    participant Tasks as UploadTasks
    participant Progress as BatchProgress
    
    Client->>Uploader: UploadFilesAsync(endpoint, filePaths, formData)
    Uploader->>Uploader: 验证所有文件
    
    alt 有文件验证失败
        Uploader-->>Client: 返回验证失败的文件列表
    else 所有文件验证通过
        Uploader->>Uploader: 创建批量上传任务
        
        loop 每个文件
            Uploader->>Semaphore: 等待并发槽位
            Semaphore-->>Uploader: 获得上传许可
            
            Uploader->>Tasks: 创建文件上传任务
            Tasks->>Tasks: 异步执行文件上传
            
            par 并发上传
                Tasks->>Uploader: UploadFileAsync(file1)
            and
                Tasks->>Uploader: UploadFileAsync(file2)
            and
                Tasks->>Uploader: UploadFileAsync(fileN)
            end
            
            Tasks->>Progress: 更新单个文件进度
            Progress->>Progress: 计算总体进度
            Progress-->>Client: 批量进度回调
            
            Tasks->>Semaphore: 释放并发槽位
        end
        
        Uploader->>Uploader: 等待所有任务完成
        Uploader->>Uploader: 汇总上传结果
        Uploader-->>Client: 返回批量上传结果
    end
```

### 批量上传实现

```csharp
public async Task<List<ETOAUploadResult>> UploadFilesAsync(string endpoint, 
    List<string> filePaths, Dictionary<string, object> formData = null)
{
    var batchId = Guid.NewGuid().ToString();
    var startTime = DateTime.Now;
    
    try
    {
        ETLogManager.Info($"[{batchId}] 开始批量上传: {filePaths.Count} 个文件");
        
        // 1. 验证所有文件
        var validFiles = new List<string>();
        var results = new List<ETOAUploadResult>();
        
        foreach (var filePath in filePaths)
        {
            if (ValidateFile(filePath))
            {
                validFiles.Add(filePath);
            }
            else
            {
                results.Add(new ETOAUploadResult
                {
                    IsSuccess = false,
                    ErrorMessage = "文件验证失败",
                    FileName = Path.GetFileName(filePath),
                    FilePath = filePath
                });
            }
        }
        
        if (validFiles.Count == 0)
        {
            ETLogManager.Warning($"[{batchId}] 没有有效的文件可以上传");
            return results;
        }
        
        // 2. 创建并发上传任务
        var uploadTasks = new List<Task<ETOAUploadResult>>();
        var totalFiles = validFiles.Count;
        var completedFiles = 0;
        
        foreach (var filePath in validFiles)
        {
            var uploadTask = Task.Run(async () =>
            {
                // 等待并发槽位
                await ConcurrencyLimiter.WaitAsync();
                
                try
                {
                    // 执行文件上传
                    var result = await UploadFileAsync(endpoint, filePath, formData);
                    
                    // 更新批量进度
                    Interlocked.Increment(ref completedFiles);
                    var batchProgress = (double)completedFiles / totalFiles * 100;
                    
                    OnBatchProgressChanged?.Invoke(new BatchUploadProgressEventArgs
                    {
                        BatchId = batchId,
                        TotalFiles = totalFiles,
                        CompletedFiles = completedFiles,
                        Progress = batchProgress,
                        CurrentFile = Path.GetFileName(filePath),
                        CurrentResult = result
                    });
                    
                    return result;
                }
                finally
                {
                    // 释放并发槽位
                    ConcurrencyLimiter.Release();
                }
            });
            
            uploadTasks.Add(uploadTask);
        }
        
        // 3. 等待所有上传任务完成
        var uploadResults = await Task.WhenAll(uploadTasks);
        results.AddRange(uploadResults);
        
        // 4. 统计上传结果
        var successCount = results.Count(r => r.IsSuccess);
        var failureCount = results.Count - successCount;
        var totalDuration = DateTime.Now - startTime;
        
        ETLogManager.Info($"[{batchId}] 批量上传完成: 成功 {successCount}, 失败 {failureCount}, 耗时: {totalDuration.TotalSeconds:F2}秒");
        
        return results;
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"[{batchId}] 批量上传异常: {ex.Message}", ex);
        throw;
    }
}
```

## 🔄 断点续传机制

```mermaid
graph TD
    A[开始断点续传] --> B[检查上传记录]
    B --> C{是否有上传记录}
    C -->|无| D[开始新的上传]
    C -->|有| E[验证文件是否变更]
    E --> F{文件是否变更}
    F -->|是| G[清除旧记录，重新上传]
    F -->|否| H[获取已上传分块信息]
    H --> I[计算剩余分块]
    I --> J[从断点继续上传]
    J --> K[更新上传记录]
    K --> L{所有分块是否完成}
    L -->|否| M[继续上传下一分块]
    L -->|是| N[请求合并文件]
    M --> J
    N --> O[验证文件完整性]
    O --> P{验证是否通过}
    P -->|是| Q[清除上传记录]
    P -->|否| R[记录验证失败]
    Q --> S[返回上传成功]
    R --> T[返回验证失败]
    D --> U[正常上传流程]
    G --> U
    
    style A fill:#e1f5fe
    style S fill:#c8e6c9
    style T fill:#ffcdd2
```

---

**📅 文档版本**: v1.0  
**🔄 最后更新**: 2024年12月  
**👨‍💻 维护团队**: ETOAAutomation开发组
