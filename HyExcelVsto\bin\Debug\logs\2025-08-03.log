﻿2025-08-03 11:15:55 [INFO] 显示设置已变更
2025-08-03 11:15:56 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 11:15:57 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-03 11:15:57 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-03 11:15:57 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 11:15:57 [INFO] 显示设置变更后TopForm关系已重建
2025-08-03 12:36:08 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:36:08 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 111876918, 新父窗口: 7742166
2025-08-03 12:36:08 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:36:08 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:36:08 [INFO] App_WorkbookActivate: 工作簿 '一点一案上传方案信息表-20250801-170610.xlsx' 激活处理完成
2025-08-03 12:36:08 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:36:08 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-03 12:36:08 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:36:08 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 12:36:08 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:36:08 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 12:36:08 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:36:08 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:36:08 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 12:36:08 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:36:09 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 12:36:09 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:36:09 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:36:09 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:36:09 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 7742166, 新父窗口: 111876918
2025-08-03 12:36:09 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-03 12:36:09 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:36:09 [INFO] App_WorkbookActivate: 工作簿 '场景清单_专用场景_ad60d545-8ea4-4b24-bd64-e2b8352ed1a1.csv' 激活处理完成
2025-08-03 12:36:09 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:36:09 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-03 12:36:09 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:36:09 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 12:36:10 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:36:10 [WARN] 检测到Excel窗口句柄变化: 7742166 -> 111876918
2025-08-03 12:36:10 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-03 12:36:10 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:36:10 [ERROR] SetTop: 设置父窗口关系失败 - 期望: 111876918, 实际: 65552
2025-08-03 12:36:10 [WARN] 父子窗口关系不正确. 当前父窗口: 65552, 期望父窗口: 111876918
2025-08-03 12:36:10 [WARN] 检测到父子窗口关系异常，尝试修复
2025-08-03 12:36:10 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-03 12:36:10 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-03 12:36:10 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-03 12:36:10 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-03 12:36:10 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:36:10 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 12:36:10 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:50:50 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-03 12:50:50 [INFO] 系统事件监控已停止
2025-08-03 12:50:50 [INFO] Excel窗口句柄监控已停止
2025-08-03 12:50:50 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-03 12:50:51 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:50:51 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 12:50:51 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:50:51 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 7742166
2025-08-03 12:50:51 [INFO] 系统事件监控已启动
2025-08-03 12:50:51 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:50:51 [INFO] App_WorkbookActivate: 工作簿 '一点一案上传方案信息表-20250801-170610.xlsx' 激活处理完成
2025-08-03 12:50:51 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:50:51 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-03 12:50:51 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:50:51 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 12:50:52 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:50:52 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 12:50:52 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:50:52 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:50:52 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 12:50:52 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:50:52 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 12:50:52 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:50:52 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:51:29 [INFO] App_WorkbookOpen: 工作簿 '2CC扩容节点清单(2).xlsx' 打开事件触发
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:51:29 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 7742166, 新父窗口: 89790590
2025-08-03 12:51:29 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 89790590)
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:51:29 [INFO] App_WorkbookOpen: 工作簿 '2CC扩容节点清单(2).xlsx' 打开处理完成
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:51:29 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 89790590)
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:51:29 [INFO] App_WorkbookActivate: 工作簿 '2CC扩容节点清单(2).xlsx' 激活处理完成
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:51:29 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 89790590)
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:51:29 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:51:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 89790590
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:51:30 [ERROR] SetTop: 设置父窗口关系失败 - 期望: 89790590, 实际: 65552
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:51:30 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 12:51:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 89790590
2025-08-03 12:51:30 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 89790590)
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:51:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 89790590
2025-08-03 12:51:30 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 89790590)
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:51:30 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-08-03 13:04:43 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-03 13:04:43 [INFO] 系统事件监控已停止
2025-08-03 13:04:43 [INFO] Excel窗口句柄监控已停止
2025-08-03 13:04:43 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-03 13:04:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 13:04:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 13:04:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 13:04:44 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 7742166
2025-08-03 13:04:44 [INFO] 系统事件监控已启动
2025-08-03 13:04:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 13:04:44 [INFO] App_WorkbookActivate: 工作簿 '一点一案上传方案信息表-20250801-170610.xlsx' 激活处理完成
2025-08-03 13:04:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 13:04:44 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-03 13:04:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 13:04:44 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 13:04:45 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 13:04:45 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 13:04:45 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 13:04:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 13:04:45 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 13:04:45 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 13:04:45 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 13:04:45 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 13:04:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
